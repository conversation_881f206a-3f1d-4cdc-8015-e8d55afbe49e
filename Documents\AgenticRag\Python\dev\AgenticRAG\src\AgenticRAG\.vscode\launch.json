{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Debug dev.py", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/dev.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/../..", "justMyCode": false, "python": "${workspaceFolder}/../../.venv/Scripts/python.exe", "preLaunchTask": "startDockerDesktop"}, {"name": "Python Debugger: Run Current Test File", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "justMyCode": false, "python": "${workspaceFolder}/../../.venv/Scripts/python.exe", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Pytest Current File", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["${file}", "-v", "-s"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "${workspaceFolder}/../../.venv/Scripts/python.exe", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: <PERSON><PERSON><PERSON> Batch Wrappers", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["tests/test_real/bats/", "-v", "-s"], "console": "integratedTerminal", "cwd": "${workspaceFolder}", "python": "${workspaceFolder}/../../.venv/Scripts/python.exe", "env": {"PYTHONPATH": "${workspaceFolder}"}}]}